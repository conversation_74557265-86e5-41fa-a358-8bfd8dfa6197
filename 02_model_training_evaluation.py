import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from sklearn.metrics import mean_absolute_percentage_error, r2_score, mean_squared_error
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
from prophet import Prophet
from statsmodels.tsa.arima.model import ARIMA
from scipy.optimize import minimize
import matplotlib.pyplot as plt

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ModelTrainingEvaluation:
    def __init__(self, data_file='原始数据.xlsx'):
        self.data = pd.read_excel(data_file)
        self.regions = ['J区', 'H区', 'L区']
        self.variables = ['Y', 'X', 'EI9']
        self.train_end_year = 2015
        self.test_start_year = 2016
        self.models_performance = {}
        self.trained_models = {}
        self.scaler = StandardScaler()
        
    def prepare_data(self, region, variable):
        """为特定区域和变量准备数据"""
        region_data = self.data[self.data['区域'] == region].copy()
        region_data = region_data.sort_values('年份')
        
        # 训练集和测试集分割
        train_data = region_data[region_data['年份'] <= self.train_end_year].copy()
        test_data = region_data[region_data['年份'] >= self.test_start_year].copy()
        
        return train_data, test_data
    
    def calculate_metrics(self, y_true, y_pred, model_name, region, variable):
        """计算评估指标"""
        mape = mean_absolute_percentage_error(y_true, y_pred) * 100
        r2 = r2_score(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        
        print(f"{model_name} - {region} - {variable}:")
        print(f"  MAPE: {mape:.4f}%")
        print(f"  R²: {r2:.4f}")
        print(f"  RMSE: {rmse:.4f}")
        
        # 检查是否符合条件
        meets_criteria = (mape <= 15.0) and (r2 >= 0.3)
        preferred_criteria = (mape <= 10.0) and (r2 >= 0.6)
        
        return {
            'MAPE': mape,
            'R2': r2,
            'RMSE': rmse,
            'meets_criteria': meets_criteria,
            'preferred_criteria': preferred_criteria
        }
    
    def train_xgboost(self, train_data, test_data, variable):
        """训练XGBoost模型"""
        X_train = train_data[['年份']].values
        y_train = train_data[variable].values
        X_test = test_data[['年份']].values
        y_test = test_data[variable].values
        
        model = xgb.XGBRegressor(n_estimators=100, random_state=42)
        model.fit(X_train, y_train)
        
        y_pred = model.predict(X_test)
        return model, y_pred, y_test
    
    def train_prophet(self, train_data, test_data, variable):
        """训练Prophet模型"""
        # 准备Prophet数据格式
        prophet_train = pd.DataFrame({
            'ds': pd.to_datetime(train_data['年份'], format='%Y'),
            'y': train_data[variable]
        })
        
        model = Prophet(yearly_seasonality=True, daily_seasonality=False, weekly_seasonality=False)
        model.fit(prophet_train)
        
        # 预测
        future = pd.DataFrame({
            'ds': pd.to_datetime(test_data['年份'], format='%Y')
        })
        forecast = model.predict(future)
        
        y_pred = forecast['yhat'].values
        y_test = test_data[variable].values
        
        return model, y_pred, y_test
    
    def train_random_forest(self, train_data, test_data, variable):
        """训练Random Forest模型"""
        X_train = train_data[['年份']].values
        y_train = train_data[variable].values
        X_test = test_data[['年份']].values
        y_test = test_data[variable].values
        
        model = RandomForestRegressor(n_estimators=100, random_state=42)
        model.fit(X_train, y_train)
        
        y_pred = model.predict(X_test)
        return model, y_pred, y_test
    
    def train_arima(self, train_data, test_data, variable):
        """训练ARIMA模型"""
        train_series = train_data[variable].values
        test_series = test_data[variable].values
        
        # 自动选择ARIMA参数
        best_aic = float('inf')
        best_order = None
        
        for p in range(0, 3):
            for d in range(0, 2):
                for q in range(0, 3):
                    try:
                        model = ARIMA(train_series, order=(p, d, q))
                        fitted_model = model.fit()
                        if fitted_model.aic < best_aic:
                            best_aic = fitted_model.aic
                            best_order = (p, d, q)
                    except:
                        continue
        
        if best_order is None:
            best_order = (1, 1, 1)
        
        model = ARIMA(train_series, order=best_order)
        fitted_model = model.fit()
        
        # 预测
        forecast = fitted_model.forecast(steps=len(test_series))
        y_pred = forecast
        y_test = test_series
        
        return fitted_model, y_pred, y_test
    
    def gm11_model(self, data):
        """GM(1,1)灰色预测模型"""
        def gm11(x0):
            n = len(x0)
            x1 = np.cumsum(x0)
            z1 = np.array([0.5 * (x1[i] + x1[i-1]) for i in range(1, n)])
            
            B = np.array([[-z1[i], 1] for i in range(n-1)])
            Y = x0[1:].reshape(-1, 1)
            
            # 使用最小二乘法求解参数
            params = np.linalg.lstsq(B, Y, rcond=None)[0]
            a, b = params[0, 0], params[1, 0]
            
            # 构建预测函数
            def predict_func(k):
                return (x0[0] - b/a) * np.exp(-a * k) + b/a
            
            return a, b, predict_func
        
        return gm11(data)
    
    def train_gm11(self, train_data, test_data, variable):
        """训练GM(1,1)模型"""
        train_values = train_data[variable].values
        test_values = test_data[variable].values
        
        a, b, predict_func = self.gm11_model(train_values)
        
        # 预测测试集
        n_train = len(train_values)
        y_pred = []
        for i in range(len(test_values)):
            pred_val = predict_func(n_train + i)
            if i > 0:
                # 计算一次累减
                pred_val = pred_val - y_pred[i-1] if i == 1 else pred_val - sum(y_pred)
            y_pred.append(pred_val)
        
        y_pred = np.array(y_pred)
        y_test = test_values
        
        return (a, b, predict_func), y_pred, y_test
    
    def train_svr(self, train_data, test_data, variable):
        """训练SVR模型"""
        X_train = train_data[['年份']].values
        y_train = train_data[variable].values
        X_test = test_data[['年份']].values
        y_test = test_data[variable].values
        
        # 标准化
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        model = SVR(kernel='rbf', C=100, gamma=0.1)
        model.fit(X_train_scaled, y_train)
        
        y_pred = model.predict(X_test_scaled)
        return model, y_pred, y_test
    
    def evaluate_all_models(self):
        """评估所有模型"""
        model_functions = {
            'XGBoost': self.train_xgboost,
            'Prophet': self.train_prophet,
            'RandomForest': self.train_random_forest,
            'ARIMA': self.train_arima,
            'GM(1,1)': self.train_gm11,
            'SVR': self.train_svr
        }
        
        print("=== 模型训练和评估 ===\n")
        
        for region in self.regions:
            for variable in self.variables:
                print(f"\n--- {region} - {variable} ---")
                train_data, test_data = self.prepare_data(region, variable)
                
                region_var_key = f"{region}_{variable}"
                self.models_performance[region_var_key] = {}
                self.trained_models[region_var_key] = {}
                
                for model_name, model_func in model_functions.items():
                    try:
                        model, y_pred, y_test = model_func(train_data, test_data, variable)
                        metrics = self.calculate_metrics(y_test, y_pred, model_name, region, variable)
                        
                        self.models_performance[region_var_key][model_name] = metrics
                        self.trained_models[region_var_key][model_name] = model
                        
                    except Exception as e:
                        print(f"{model_name} - {region} - {variable}: 训练失败 - {e}")
                        continue
                
                print()
    
    def select_best_models(self):
        """选择最佳模型"""
        print("\n=== 模型选择结果 ===")
        
        selected_models = {}
        
        for region in self.regions:
            for variable in self.variables:
                region_var_key = f"{region}_{variable}"
                
                if region_var_key not in self.models_performance:
                    continue
                
                # 筛选符合条件的模型
                valid_models = {}
                for model_name, metrics in self.models_performance[region_var_key].items():
                    if metrics['meets_criteria']:
                        valid_models[model_name] = metrics
                
                print(f"\n{region} - {variable}:")
                print(f"  符合条件的模型数量: {len(valid_models)}")
                
                if len(valid_models) == 0:
                    print("  警告：没有模型符合基本条件！")
                    continue
                
                # 选择最佳模型（优先考虑preferred_criteria，然后考虑MAPE最小）
                best_model = None
                best_score = float('inf')
                
                for model_name, metrics in valid_models.items():
                    # 计算综合得分（MAPE越小越好，R2越大越好）
                    score = metrics['MAPE'] - metrics['R2'] * 10
                    if score < best_score:
                        best_score = score
                        best_model = model_name
                
                if best_model:
                    selected_models[region_var_key] = {
                        'model_name': best_model,
                        'metrics': valid_models[best_model],
                        'model': self.trained_models[region_var_key][best_model]
                    }
                    
                    print(f"  最佳模型: {best_model}")
                    print(f"    MAPE: {valid_models[best_model]['MAPE']:.4f}%")
                    print(f"    R²: {valid_models[best_model]['R2']:.4f}")
                    print(f"    RMSE: {valid_models[best_model]['RMSE']:.4f}")
        
        return selected_models

def main():
    # 创建模型训练评估器
    evaluator = ModelTrainingEvaluation()
    
    # 评估所有模型
    evaluator.evaluate_all_models()
    
    # 选择最佳模型
    selected_models = evaluator.select_best_models()
    
    print(f"\n总共选择了 {len(selected_models)} 个最佳模型")
    
    return evaluator, selected_models

if __name__ == "__main__":
    evaluator, selected_models = main() 