import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from sklearn.metrics import mean_absolute_percentage_error, r2_score, mean_squared_error
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
import xgboost as xgb
from prophet import Prophet
from statsmodels.tsa.arima.model import ARIMA
import matplotlib.pyplot as plt

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ImprovedModelTraining:
    def __init__(self, data_file='原始数据.xlsx'):
        self.data = pd.read_excel(data_file)
        self.regions = ['J区', 'H区', 'L区']
        self.variables = ['Y', 'X', 'EI9']
        self.train_end_year = 2015
        self.test_start_year = 2016
        self.models_performance = {}
        self.trained_models = {}
        self.selected_models = {}
        
    def create_features(self, data):
        """创建更丰富的特征"""
        features = pd.DataFrame()
        
        # 基础时间特征
        features['year'] = data['年份']
        features['year_norm'] = (data['年份'] - data['年份'].min()) / (data['年份'].max() - data['年份'].min())
        
        # 多项式特征
        features['year_squared'] = features['year'] ** 2
        features['year_cubed'] = features['year'] ** 3
        
        # 相对于起始年份的时间
        features['time_from_start'] = data['年份'] - data['年份'].min()
        
        # 滞后特征（如果有足够的数据）
        if len(data) > 3:
            features['lag_1'] = data[self.current_variable].shift(1)
            features['lag_2'] = data[self.current_variable].shift(2)
            features['lag_3'] = data[self.current_variable].shift(3)
        
        # 移动平均特征
        if len(data) > 3:
            features['ma_3'] = data[self.current_variable].rolling(window=3, min_periods=1).mean()
            features['ma_5'] = data[self.current_variable].rolling(window=5, min_periods=1).mean()
        
        # 趋势特征
        if len(data) > 1:
            features['trend'] = np.arange(len(data))
            features['trend_norm'] = features['trend'] / len(data)
        
        # 填充缺失值
        features = features.fillna(method='bfill').fillna(method='ffill')
        
        return features
    
    def prepare_data_improved(self, region, variable):
        """改进的数据准备方法"""
        region_data = self.data[self.data['区域'] == region].copy()
        region_data = region_data.sort_values('年份').reset_index(drop=True)
        
        self.current_variable = variable
        
        # 创建特征
        features = self.create_features(region_data)
        
        # 目标变量
        target = region_data[variable].values
        
        # 分割数据
        train_mask = region_data['年份'] <= self.train_end_year
        test_mask = region_data['年份'] >= self.test_start_year
        
        X_train = features[train_mask].dropna()
        X_test = features[test_mask].dropna()
        y_train = target[train_mask][:len(X_train)]
        y_test = target[test_mask][:len(X_test)]
        
        return X_train, X_test, y_train, y_test, region_data
    
    def calculate_metrics(self, y_true, y_pred, model_name, region, variable):
        """计算评估指标"""
        # 确保没有无穷大或NaN值
        y_pred = np.nan_to_num(y_pred, nan=0.0, posinf=1e6, neginf=-1e6)
        
        mape = mean_absolute_percentage_error(y_true, y_pred) * 100
        r2 = r2_score(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        
        print(f"{model_name} - {region} - {variable}:")
        print(f"  MAPE: {mape:.4f}%")
        print(f"  R²: {r2:.4f}")
        print(f"  RMSE: {rmse:.4f}")
        
        # 检查是否符合条件
        meets_criteria = (mape <= 15.0) and (r2 >= 0.3)
        preferred_criteria = (mape <= 10.0) and (r2 >= 0.6)
        
        return {
            'MAPE': mape,
            'R2': r2,
            'RMSE': rmse,
            'meets_criteria': meets_criteria,
            'preferred_criteria': preferred_criteria
        }
    
    def train_polynomial_regression(self, X_train, X_test, y_train, y_test):
        """多项式回归"""
        from sklearn.linear_model import LinearRegression
        from sklearn.pipeline import Pipeline
        
        # 只使用基础的年份特征进行多项式扩展
        X_train_simple = X_train[['year_norm']].copy()
        X_test_simple = X_test[['year_norm']].copy()
        
        model = Pipeline([
            ('poly', PolynomialFeatures(degree=3)),
            ('linear', LinearRegression())
        ])
        
        model.fit(X_train_simple, y_train)
        y_pred = model.predict(X_test_simple)
        
        return model, y_pred, y_test
    
    def train_xgboost_improved(self, X_train, X_test, y_train, y_test):
        """改进的XGBoost模型"""
        # 选择数值特征
        numeric_cols = X_train.select_dtypes(include=[np.number]).columns
        X_train_clean = X_train[numeric_cols].fillna(0)
        X_test_clean = X_test[numeric_cols].fillna(0)
        
        model = xgb.XGBRegressor(
            n_estimators=50,
            max_depth=3,
            learning_rate=0.1,
            random_state=42,
            reg_alpha=0.1,
            reg_lambda=0.1
        )
        
        model.fit(X_train_clean, y_train)
        y_pred = model.predict(X_test_clean)
        
        return model, y_pred, y_test
    
    def train_random_forest_improved(self, X_train, X_test, y_train, y_test):
        """改进的随机森林模型"""
        numeric_cols = X_train.select_dtypes(include=[np.number]).columns
        X_train_clean = X_train[numeric_cols].fillna(0)
        X_test_clean = X_test[numeric_cols].fillna(0)
        
        model = RandomForestRegressor(
            n_estimators=50,
            max_depth=5,
            random_state=42,
            min_samples_split=3,
            min_samples_leaf=2
        )
        
        model.fit(X_train_clean, y_train)
        y_pred = model.predict(X_test_clean)
        
        return model, y_pred, y_test
    
    def train_simple_trend(self, X_train, X_test, y_train, y_test):
        """简单趋势模型"""
        from sklearn.linear_model import LinearRegression
        
        # 只使用年份作为特征
        X_train_simple = X_train[['year']].copy()
        X_test_simple = X_test[['year']].copy()
        
        model = LinearRegression()
        model.fit(X_train_simple, y_train)
        y_pred = model.predict(X_test_simple)
        
        return model, y_pred, y_test
    
    def train_arima_improved(self, region_data, variable):
        """改进的ARIMA模型"""
        train_data = region_data[region_data['年份'] <= self.train_end_year]
        test_data = region_data[region_data['年份'] >= self.test_start_year]
        
        train_series = train_data[variable].values
        test_series = test_data[variable].values
        
        # 简单的ARIMA(1,1,1)
        try:
            model = ARIMA(train_series, order=(1, 1, 1))
            fitted_model = model.fit()
            forecast = fitted_model.forecast(steps=len(test_series))
            y_pred = forecast
            y_test = test_series
            
            return fitted_model, y_pred, y_test
        except:
            # 如果ARIMA失败，使用简单的移动平均
            last_values = train_series[-3:]
            y_pred = np.full(len(test_series), np.mean(last_values))
            return None, y_pred, test_series
    
    def train_prophet_improved(self, region_data, variable):
        """改进的Prophet模型"""
        train_data = region_data[region_data['年份'] <= self.train_end_year]
        test_data = region_data[region_data['年份'] >= self.test_start_year]
        
        try:
            # 准备Prophet数据
            prophet_data = pd.DataFrame({
                'ds': pd.to_datetime(train_data['年份'], format='%Y'),
                'y': train_data[variable]
            })
            
            model = Prophet(
                yearly_seasonality=False,
                daily_seasonality=False,
                weekly_seasonality=False,
                changepoint_prior_scale=0.05
            )
            
            model.fit(prophet_data)
            
            # 预测
            future = pd.DataFrame({
                'ds': pd.to_datetime(test_data['年份'], format='%Y')
            })
            forecast = model.predict(future)
            
            y_pred = forecast['yhat'].values
            y_test = test_data[variable].values
            
            return model, y_pred, y_test
        except:
            # 如果Prophet失败，使用简单预测
            last_values = train_data[variable].values[-3:]
            y_pred = np.full(len(test_data), np.mean(last_values))
            return None, y_pred, test_data[variable].values
    
    def evaluate_all_models_improved(self):
        """评估所有改进的模型"""
        print("=== 改进的模型训练和评估 ===\n")
        
        for region in self.regions:
            for variable in self.variables:
                print(f"\n--- {region} - {variable} ---")
                
                try:
                    X_train, X_test, y_train, y_test, region_data = self.prepare_data_improved(region, variable)
                    
                    region_var_key = f"{region}_{variable}"
                    self.models_performance[region_var_key] = {}
                    self.trained_models[region_var_key] = {}
                    
                    # 模型列表
                    models_to_try = [
                        ('SimpleLinear', self.train_simple_trend),
                        ('Polynomial', self.train_polynomial_regression),
                        ('XGBoost', self.train_xgboost_improved),
                        ('RandomForest', self.train_random_forest_improved),
                    ]
                    
                    # 训练基于特征的模型
                    for model_name, model_func in models_to_try:
                        try:
                            if len(X_train) > 0 and len(X_test) > 0:
                                model, y_pred, y_test_actual = model_func(X_train, X_test, y_train, y_test)
                                metrics = self.calculate_metrics(y_test_actual, y_pred, model_name, region, variable)
                                
                                self.models_performance[region_var_key][model_name] = metrics
                                self.trained_models[region_var_key][model_name] = model
                        except Exception as e:
                            print(f"{model_name} - {region} - {variable}: 训练失败 - {e}")
                    
                    # 训练时间序列模型
                    try:
                        model, y_pred, y_test_actual = self.train_arima_improved(region_data, variable)
                        metrics = self.calculate_metrics(y_test_actual, y_pred, 'ARIMA', region, variable)
                        self.models_performance[region_var_key]['ARIMA'] = metrics
                        self.trained_models[region_var_key]['ARIMA'] = model
                    except Exception as e:
                        print(f"ARIMA - {region} - {variable}: 训练失败 - {e}")
                    
                    try:
                        model, y_pred, y_test_actual = self.train_prophet_improved(region_data, variable)
                        metrics = self.calculate_metrics(y_test_actual, y_pred, 'Prophet', region, variable)
                        self.models_performance[region_var_key]['Prophet'] = metrics
                        self.trained_models[region_var_key]['Prophet'] = model
                    except Exception as e:
                        print(f"Prophet - {region} - {variable}: 训练失败 - {e}")
                        
                except Exception as e:
                    print(f"数据准备失败 - {region} - {variable}: {e}")
                    continue
                
                print()
    
    def select_best_models(self):
        """选择最佳模型"""
        print("\n=== 模型选择结果 ===")
        
        selected_models = {}
        summary_stats = []
        
        for region in self.regions:
            for variable in self.variables:
                region_var_key = f"{region}_{variable}"
                
                if region_var_key not in self.models_performance:
                    continue
                
                # 筛选符合条件的模型
                valid_models = {}
                for model_name, metrics in self.models_performance[region_var_key].items():
                    if metrics['meets_criteria']:
                        valid_models[model_name] = metrics
                
                print(f"\n{region} - {variable}:")
                print(f"  符合条件的模型数量: {len(valid_models)}")
                
                if len(valid_models) == 0:
                    print("  警告：没有模型符合基本条件！尝试选择最佳模型...")
                    # 如果没有符合条件的模型，选择R²最高的
                    best_r2_model = None
                    best_r2 = -float('inf')
                    for model_name, metrics in self.models_performance[region_var_key].items():
                        if metrics['R2'] > best_r2:
                            best_r2 = metrics['R2']
                            best_r2_model = model_name
                    
                    if best_r2_model:
                        valid_models = {best_r2_model: self.models_performance[region_var_key][best_r2_model]}
                        print(f"  选择R²最高的模型: {best_r2_model} (R²={best_r2:.4f})")
                
                # 选择最佳模型
                if valid_models:
                    best_model = None
                    best_score = float('inf')
                    
                    for model_name, metrics in valid_models.items():
                        # 计算综合得分
                        score = metrics['MAPE'] - metrics['R2'] * 10
                        if score < best_score:
                            best_score = score
                            best_model = model_name
                    
                    if best_model:
                        selected_models[region_var_key] = {
                            'model_name': best_model,
                            'metrics': valid_models[best_model],
                            'model': self.trained_models[region_var_key][best_model]
                        }
                        
                        metrics = valid_models[best_model]
                        print(f"  最佳模型: {best_model}")
                        print(f"    MAPE: {metrics['MAPE']:.4f}%")
                        print(f"    R²: {metrics['R2']:.4f}")
                        print(f"    RMSE: {metrics['RMSE']:.4f}")
                        
                        # 收集汇总统计
                        summary_stats.append({
                            'Region': region,
                            'Variable': variable,
                            'Model': best_model,
                            'MAPE': metrics['MAPE'],
                            'R2': metrics['R2'],
                            'RMSE': metrics['RMSE'],
                            'Meets_Criteria': metrics['meets_criteria']
                        })
        
        self.selected_models = selected_models
        
        # 保存模型选择摘要
        if summary_stats:
            summary_df = pd.DataFrame(summary_stats)
            summary_df.to_csv('model_selection_summary.csv', index=False, encoding='utf-8-sig')
            print(f"\n模型选择摘要已保存到 model_selection_summary.csv")
        
        return selected_models

def main():
    # 创建改进的模型训练评估器
    evaluator = ImprovedModelTraining()
    
    # 评估所有模型
    evaluator.evaluate_all_models_improved()
    
    # 选择最佳模型
    selected_models = evaluator.select_best_models()
    
    print(f"\n总共选择了 {len(selected_models)} 个最佳模型")
    
    # 检查是否有至少3个符合条件的模型
    meets_criteria_count = 0
    for region_var, model_info in selected_models.items():
        if model_info['metrics']['meets_criteria']:
            meets_criteria_count += 1
    
    print(f"其中 {meets_criteria_count} 个模型符合基本条件")
    
    if meets_criteria_count < 3:
        print("警告：符合条件的模型少于3个，建议进一步调整模型参数或特征工程")
    
    return evaluator, selected_models

if __name__ == "__main__":
    evaluator, selected_models = main() 