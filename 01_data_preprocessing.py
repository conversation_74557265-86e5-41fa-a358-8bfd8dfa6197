import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

class DataPreprocessor:
    def __init__(self, file_path):
        self.file_path = file_path
        self.data = None
        self.regions = ['J区', 'H区', 'L区']
        
    def load_data(self):
        """加载Excel数据"""
        try:
            # 尝试读取Excel文件
            self.data = pd.read_excel(self.file_path)
            print("数据加载成功！")
            print(f"数据形状: {self.data.shape}")
            print("\n数据前5行:")
            print(self.data.head())
            print("\n数据列名:")
            print(self.data.columns.tolist())
            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def analyze_data_structure(self):
        """分析数据结构"""
        if self.data is None:
            print("请先加载数据")
            return
        
        print("\n=== 数据结构分析 ===")
        print(f"总行数: {len(self.data)}")
        print(f"总列数: {len(self.data.columns)}")
        print("\n各列数据类型:")
        print(self.data.dtypes)
        print("\n缺失值统计:")
        print(self.data.isnull().sum())
        
        # 检查年份范围
        if '年份' in self.data.columns or 'year' in self.data.columns:
            year_col = '年份' if '年份' in self.data.columns else 'year'
            print(f"\n年份范围: {self.data[year_col].min()} - {self.data[year_col].max()}")
        
    def prepare_time_series_data(self):
        """准备时间序列数据"""
        if self.data is None:
            print("请先加载数据")
            return None
        
        # 根据实际数据结构调整列名
        # 假设数据包含年份、区域、X、EI9、Y等列
        processed_data = {}
        
        print("\n=== 准备时间序列数据 ===")
        
        # 如果数据结构不明确，先展示数据样本
        print("数据样本:")
        print(self.data.head(10))
        
        return self.data
    
    def split_train_test(self, start_year=1990, end_year=2022):
        """按时间划分训练集和测试集"""
        total_years = end_year - start_year + 1  # 33年
        train_years = int(total_years * 0.8)  # 26年
        test_years = total_years - train_years  # 7年
        
        train_end_year = start_year + train_years - 1  # 2015年
        test_start_year = train_end_year + 1  # 2016年
        
        print(f"\n=== 数据划分 ===")
        print(f"总年份: {start_year}-{end_year} ({total_years}年)")
        print(f"训练集: {start_year}-{train_end_year} ({train_years}年, 80%)")
        print(f"测试集: {test_start_year}-{end_year} ({test_years}年, 20%)")
        
        return train_end_year, test_start_year

def main():
    # 创建数据预处理器
    preprocessor = DataPreprocessor('原始数据.xlsx')
    
    # 加载数据
    if preprocessor.load_data():
        # 分析数据结构
        preprocessor.analyze_data_structure()
        
        # 准备时间序列数据
        data = preprocessor.prepare_time_series_data()
        
        # 划分训练测试集
        train_end, test_start = preprocessor.split_train_test()
        
        print("\n数据预处理完成！")
        
        return preprocessor.data
    else:
        print("数据预处理失败！")
        return None

if __name__ == "__main__":
    data = main() 